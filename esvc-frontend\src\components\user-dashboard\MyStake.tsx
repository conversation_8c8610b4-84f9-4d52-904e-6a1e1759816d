import React, { useState } from 'react';
import '../../styles/components/user-dashboard/MyStake.css';
import UserPageLayout from '../layouts/UserPageLayout';
import UserSideNav from './UserSideNav';

// Import icons
import cardCoinIcon from '../../assets/card-coin.png';

interface MyStakeProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const MyStake: React.FC<MyStakeProps> = () => {
  const [activeTab, setActiveTab] = useState('my-stake');
  const [showBalances, setShowBalances] = useState(true);
  const [selectedWallet, setSelectedWallet] = useState('wallet1');

  const toggleBalances = () => {
    setShowBalances(!showBalances);
  };

  // Header controls for the greeting section
  const headerControls = (
    <>
      <button className="stake-esvc-btn">
        <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
        Stake ESVC
      </button>

      <div className="balance-toggle">
        <span className="toggle-label">Show balances</span>
        <label className="toggle-switch">
          <input
            type="checkbox"
            checked={showBalances}
            onChange={toggleBalances}
          />
          <span className="toggle-slider"></span>
        </label>
        <span className="toggle-label">Hide balances</span>
      </div>
    </>
  );

  return (
    <UserPageLayout
      className="my-stake-container"
      showGreetingSection={true}
      greetingText="Hi, Oluwatosin 👋"
      greetingSubtitle="Here is your staking overview"
      headerControls={headerControls}
    >
      <div className="my-stake-content">
        <div className="dashboard-layout">
          {/* Sidebar */}
          <UserSideNav
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Dashboard Content */}
          <div className="dashboard-content">
            {/* Wallet Selector Section */}
            <div className="wallet-selector-section">
              <div className="wallet-selector-card">
                <select 
                  className="wallet-dropdown"
                  value={selectedWallet}
                  onChange={(e) => setSelectedWallet(e.target.value)}
                >
                  <option value="wallet1">🔒 Wallet 1 ($10,000 stakes)</option>
                  <option value="wallet2">🔒 Wallet 2 ($5,000 stakes)</option>
                  <option value="wallet3">🔒 Wallet 3 ($15,000 stakes)</option>
                </select>
              </div>
            </div>

            {/* Stake Details Section */}
            <div className="stake-details-section">
              <div className="stake-details-card">
                <div className="stake-header">
                  <div className="stake-title-section">
                    <h2 className="stake-title">
                      {showBalances ? '788.50' : '***.**'}ESVC
                    </h2>
                    <p className="stake-subtitle">
                      {showBalances ? '$700 ESVC staked so far' : '$*** ESVC staked so far'}
                    </p>
                  </div>
                  <div className="stake-stats">
                    <div className="stat-item">
                      <div className="stat-value">
                        {showBalances ? '$700' : '$***'}
                      </div>
                      <div className="stat-label">Total Expected ROI</div>
                    </div>
                    <button className="withdraw-earned-btn">
                      Withdraw Earned ROI
                    </button>
                  </div>
                </div>

                {/* Stake Info Grid */}
                <div className="stake-info-grid">
                  <div className="info-column">
                    <div className="info-row">
                      <span className="info-label">Amount Staked</span>
                      <span className="info-value">
                        {showBalances ? '788.50ESVC' : '***.**ESVC'}
                      </span>
                    </div>
                    <div className="info-row">
                      <span className="info-label">Lock Period</span>
                      <span className="info-value">12 months</span>
                    </div>
                    <div className="info-row">
                      <span className="info-label">Unstake Date</span>
                      <span className="info-value">Jan 3, 2026</span>
                    </div>
                  </div>
                  <div className="info-column">
                    <div className="info-row">
                      <span className="info-label">Status</span>
                      <span className="info-value status-active">Active</span>
                    </div>
                    <div className="info-row">
                      <span className="info-label">Date Staked</span>
                      <span className="info-value">Jan 3, 2025</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserPageLayout>
  );
};

export default MyStake;
