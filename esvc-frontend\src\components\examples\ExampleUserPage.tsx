import React, { useState } from 'react';
import UserPageLayout from '../layouts/UserPageLayout';
import '../../styles/components/examples/ExampleUserPage.css';

const ExampleUserPage: React.FC = () => {
  const [showBalances, setShowBalances] = useState(true);

  // Example header controls (similar to user overview)
  const headerControls = (
    <>
      <button className="stake-esvc-btn">
        <span>Stake ESVC</span>
      </button>
      <div className="balance-toggle">
        <span className="toggle-label">Show balances</span>
        <label className="toggle-switch">
          <input
            type="checkbox"
            checked={showBalances}
            onChange={(e) => setShowBalances(e.target.checked)}
          />
          <span className="toggle-slider"></span>
        </label>
        <span className="toggle-label">Hide balances</span>
      </div>
    </>
  );

  return (
    <UserPageLayout
      className="example-user-page"
      showGreetingSection={true}
      greetingText="Hi, <PERSON>luwatosin 👋"
      greetingSubtitle="Welcome to your custom page"
      headerControls={headerControls}
    >
      {/* Example content */}
      <div className="example-content">
        <div className="content-section">
          <h2 className="section-title">Example Section</h2>
          <div className="content-card">
            <p>This is an example of how to use the UserPageLayout component.</p>
            <p>You can customize:</p>
            <ul>
              <li>Page title and subtitle</li>
              <li>Greeting section with custom text</li>
              <li>Header controls (buttons, toggles, etc.)</li>
              <li>Background blur gradients</li>
              <li>Show/hide navigation and footer</li>
            </ul>
          </div>
        </div>

        <div className="content-section">
          <h2 className="section-title">Another Section</h2>
          <div className="content-grid">
            <div className="content-card">
              <h3>Card 1</h3>
              <p>Content for card 1</p>
            </div>
            <div className="content-card">
              <h3>Card 2</h3>
              <p>Content for card 2</p>
            </div>
            <div className="content-card">
              <h3>Card 3</h3>
              <p>Content for card 3</p>
            </div>
          </div>
        </div>
      </div>
    </UserPageLayout>
  );
};

export default ExampleUserPage;
